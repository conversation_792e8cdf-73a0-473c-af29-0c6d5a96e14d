import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import crypto from 'crypto';

// File type validation
export const ALLOWED_MIME_TYPES = [
  // Text and documents
  'text/plain', 'text/csv',
  'application/pdf',
  'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  
  // Images
  'image/jpeg', 'image/png', 'image/tiff', 'image/vnd.adobe.photoshop',
  
  // Video
  'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv',
  'video/webm', 'video/x-flv', 'video/x-matroska',
  
  // Audio
  'audio/mpeg', 'audio/wav', 'audio/flac', 'audio/aac', 'audio/ogg',
  'audio/x-ms-wma', 'audio/mp4',
  
  // Medical/Scientific
  'application/dicom',
  
  // Database and other
  'application/octet-stream', // For various binary files
  'application/x-sqlite3',
  'application/vnd.sqlite3'
];

export const ALLOWED_EXTENSIONS = [
  // Text and documents
  '.txt', '.csv', '.pdf', '.xls', '.xlsx', '.doc', '.docx', '.ppt', '.pptx', '.psd',
  
  // Images
  '.jpg', '.jpeg', '.png', '.tiff', '.tif',
  
  // Video
  '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.y4m',
  
  // Audio
  '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
  
  // Medical/Scientific
  '.dcm', '.dicom',
  
  // Database and other
  '.db', '.sqlite', '.sqlite3'
];

// File size limits (100GB = 107,374,182,400 bytes)
export const MAX_FILE_SIZE = 107374182400; // 100GB
export const MAX_FILE_SIZE_MB = 102400; // 100GB in MB

// Rate limiting configuration
export const createRateLimiter = (windowMs: number, max: number, message: string) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: 'Rate limit exceeded',
      details: message,
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      console.warn(`🚨 Rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        error: 'Rate limit exceeded',
        details: message,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// Upload rate limiter - 5 uploads per hour per IP
export const uploadRateLimit = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  5, // 5 uploads
  'Maximum 5 uploads per hour allowed'
);

// General API rate limiter - 100 requests per 15 minutes per IP
export const apiRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests
  'Maximum 100 API requests per 15 minutes allowed'
);

// File validation middleware
export const validateFile = (req: Request, res: Response, next: NextFunction) => {
  if (!req.file) {
    return res.status(400).json({
      error: 'No file uploaded',
      details: 'A file is required for upload'
    });
  }

  const file = req.file;
  const fileExtension = file.originalname.toLowerCase().split('.').pop();

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return res.status(413).json({
      error: 'File too large',
      details: `Maximum file size is ${MAX_FILE_SIZE_MB}GB. Your file is ${(file.size / 1024 / 1024 / 1024).toFixed(2)}GB`,
      maxSize: MAX_FILE_SIZE,
      actualSize: file.size
    });
  }

  // Check file extension
  if (fileExtension && !ALLOWED_EXTENSIONS.includes(`.${fileExtension}`)) {
    return res.status(415).json({
      error: 'Unsupported file type',
      details: `File extension '.${fileExtension}' is not supported`,
      allowedExtensions: ALLOWED_EXTENSIONS,
      receivedExtension: `.${fileExtension}`
    });
  }

  // Check MIME type (if provided)
  if (file.mimetype && !ALLOWED_MIME_TYPES.includes(file.mimetype)) {
    console.warn(`⚠️ Unsupported MIME type: ${file.mimetype} for file: ${file.originalname}`);
    // Don't reject based on MIME type alone as it can be unreliable
  }

  // Validate filename
  if (!isValidFilename(file.originalname)) {
    return res.status(400).json({
      error: 'Invalid filename',
      details: 'Filename contains invalid characters or is too long',
      filename: file.originalname
    });
  }

  console.log(`✅ File validation passed: ${file.originalname} (${file.size} bytes)`);
  next();
};

// Filename validation
const isValidFilename = (filename: string): boolean => {
  // Check length
  if (filename.length > 255) {
    return false;
  }

  // Check for invalid characters
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(filename)) {
    return false;
  }

  // Check for reserved names (Windows)
  const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
  if (reservedNames.test(filename)) {
    return false;
  }

  return true;
};

// Security headers middleware
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for file uploads
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// Request logging middleware
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId = crypto.randomUUID();
  
  // Add request ID to request object
  (req as any).requestId = requestId;
  
  console.log(`📥 ${req.method} ${req.path} - ${req.ip} [${requestId}]`);
  
  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const statusColor = res.statusCode >= 400 ? '❌' : '✅';
    console.log(`📤 ${statusColor} ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms [${requestId}]`);
  });
  
  next();
};

// IP whitelist middleware (optional)
export const createIPWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || '';
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      console.warn(`🚨 Blocked request from unauthorized IP: ${clientIP}`);
      return res.status(403).json({
        error: 'Access denied',
        details: 'Your IP address is not authorized to access this service'
      });
    }
    
    next();
  };
};

// Health check endpoint
export const healthCheck = (req: Request, res: Response) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0'
  });
};

// Error handling middleware
export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  const requestId = (req as any).requestId || 'unknown';
  
  console.error(`❌ Error in request [${requestId}]:`, error);
  
  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    error: 'Internal server error',
    requestId,
    details: isDevelopment ? error.message : 'An unexpected error occurred',
    timestamp: new Date().toISOString()
  });
};
