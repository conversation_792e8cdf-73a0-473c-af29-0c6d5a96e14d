import fs from 'fs';
import path from 'path';

export interface UploadMetrics {
  transferId: string;
  timestamp: number;
  originalFilename: string;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  compressionTime: number;
  uploadTime: number;
  totalProcessingTime: number;
  compressionMethod: string;
  fileExtension: string;
  uploadSpeed: number; // MB/s
  success: boolean;
  errorMessage?: string;
}

export interface SystemMetrics {
  timestamp: number;
  diskUsage: {
    uploadDir: number;
    workDir: number;
  };
  memoryUsage: {
    used: number;
    free: number;
    total: number;
  };
  activeJobs: number;
}

export class EC2Analytics {
  private metricsFile: string;
  private systemMetricsFile: string;
  private logDir: string;

  constructor(logDir: string = '/var/log/fasttransfer') {
    this.logDir = logDir;
    this.metricsFile = path.join(logDir, 'upload-metrics.jsonl');
    this.systemMetricsFile = path.join(logDir, 'system-metrics.jsonl');
    
    // Ensure log directory exists
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
        console.log(`📊 Created analytics log directory: ${this.logDir}`);
      }
    } catch (error) {
      console.error('❌ Failed to create log directory:', error);
      // Fallback to current directory
      this.logDir = './logs';
      this.metricsFile = path.join(this.logDir, 'upload-metrics.jsonl');
      this.systemMetricsFile = path.join(this.logDir, 'system-metrics.jsonl');
      
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
      }
    }
  }

  async logUploadMetrics(metrics: UploadMetrics): Promise<void> {
    try {
      const logEntry = JSON.stringify(metrics) + '\n';
      fs.appendFileSync(this.metricsFile, logEntry);
      
      console.log('📊 Upload metrics logged:', {
        transferId: metrics.transferId,
        compressionRatio: `${metrics.compressionRatio.toFixed(2)}%`,
        compressionTime: `${metrics.compressionTime}ms`,
        totalTime: `${metrics.totalProcessingTime}ms`,
        success: metrics.success
      });
    } catch (error) {
      console.error('❌ Failed to log upload metrics:', error);
    }
  }

  async logSystemMetrics(): Promise<void> {
    try {
      const metrics = await this.collectSystemMetrics();
      const logEntry = JSON.stringify(metrics) + '\n';
      fs.appendFileSync(this.systemMetricsFile, logEntry);
      
      console.log('📊 System metrics logged:', {
        diskUsage: `${(metrics.diskUsage.uploadDir / 1024 / 1024).toFixed(2)}MB upload, ${(metrics.diskUsage.workDir / 1024 / 1024).toFixed(2)}MB work`,
        memoryUsage: `${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB used`,
        activeJobs: metrics.activeJobs
      });
    } catch (error) {
      console.error('❌ Failed to log system metrics:', error);
    }
  }

  private async collectSystemMetrics(): Promise<SystemMetrics> {
    const uploadDir = process.env.EC2_UPLOAD_DIR || '/mnt/data/uploads';
    const workDir = process.env.EC2_WORK_DIR || '/mnt/data/work';
    
    return {
      timestamp: Date.now(),
      diskUsage: {
        uploadDir: this.getDirectorySize(uploadDir),
        workDir: this.getDirectorySize(workDir)
      },
      memoryUsage: {
        used: process.memoryUsage().heapUsed,
        free: process.memoryUsage().heapTotal - process.memoryUsage().heapUsed,
        total: process.memoryUsage().heapTotal
      },
      activeJobs: 0 // TODO: Implement active job tracking
    };
  }

  private getDirectorySize(dirPath: string): number {
    try {
      if (!fs.existsSync(dirPath)) {
        return 0;
      }

      let totalSize = 0;
      const files = fs.readdirSync(dirPath);

      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += stats.size;
        }
      }

      return totalSize;
    } catch (error) {
      console.error(`❌ Failed to calculate directory size for ${dirPath}:`, error);
      return 0;
    }
  }

  async getUploadStats(hours: number = 24): Promise<{
    totalUploads: number;
    successfulUploads: number;
    failedUploads: number;
    averageCompressionRatio: number;
    averageCompressionTime: number;
    totalDataProcessed: number;
    totalDataSaved: number;
  }> {
    try {
      const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
      const metrics = this.readMetricsFile(this.metricsFile, cutoffTime);

      const totalUploads = metrics.length;
      const successfulUploads = metrics.filter(m => m.success).length;
      const failedUploads = totalUploads - successfulUploads;

      const successfulMetrics = metrics.filter(m => m.success);
      const averageCompressionRatio = successfulMetrics.length > 0 
        ? successfulMetrics.reduce((sum, m) => sum + m.compressionRatio, 0) / successfulMetrics.length 
        : 0;

      const averageCompressionTime = successfulMetrics.length > 0
        ? successfulMetrics.reduce((sum, m) => sum + m.compressionTime, 0) / successfulMetrics.length
        : 0;

      const totalDataProcessed = successfulMetrics.reduce((sum, m) => sum + m.originalSize, 0);
      const totalDataSaved = successfulMetrics.reduce((sum, m) => sum + (m.originalSize - m.compressedSize), 0);

      return {
        totalUploads,
        successfulUploads,
        failedUploads,
        averageCompressionRatio,
        averageCompressionTime,
        totalDataProcessed,
        totalDataSaved
      };
    } catch (error) {
      console.error('❌ Failed to get upload stats:', error);
      return {
        totalUploads: 0,
        successfulUploads: 0,
        failedUploads: 0,
        averageCompressionRatio: 0,
        averageCompressionTime: 0,
        totalDataProcessed: 0,
        totalDataSaved: 0
      };
    }
  }

  private readMetricsFile(filePath: string, cutoffTime: number): UploadMetrics[] {
    try {
      if (!fs.existsSync(filePath)) {
        return [];
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());
      
      return lines
        .map(line => {
          try {
            return JSON.parse(line) as UploadMetrics;
          } catch {
            return null;
          }
        })
        .filter((metrics): metrics is UploadMetrics => 
          metrics !== null && metrics.timestamp >= cutoffTime
        );
    } catch (error) {
      console.error(`❌ Failed to read metrics file ${filePath}:`, error);
      return [];
    }
  }

  async startSystemMonitoring(intervalMinutes: number = 5): Promise<void> {
    console.log(`📊 Starting system monitoring (every ${intervalMinutes} minutes)`);
    
    setInterval(async () => {
      await this.logSystemMetrics();
    }, intervalMinutes * 60 * 1000);

    // Log initial metrics
    await this.logSystemMetrics();
  }
}

export const analytics = new EC2Analytics();
