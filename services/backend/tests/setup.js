// Jest setup file for EC2 upload tests

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.AWS_REGION = 'us-east-1';
process.env.COMPRESSED_BUCKET = 'test-bucket';
process.env.MAX_FILE_SIZE = '107374182400'; // 100GB
process.env.UPLOAD_RATE_LIMIT = '1000'; // High limit for testing
process.env.API_RATE_LIMIT = '10000'; // High limit for testing

// Mock AWS SDK to avoid actual AWS calls during testing
jest.mock('@aws-sdk/client-s3', () => {
  const mockS3Client = {
    send: jest.fn().mockResolvedValue({
      Location: 'https://test-bucket.s3.amazonaws.com/test-file.zmt',
      ETag: '"test-etag"',
      Key: 'test-file.zmt'
    }),
    config: {
      region: 'us-east-1',
      credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
      }
    }
  };

  return {
    S3Client: jest.fn(() => mockS3Client),
    PutObjectCommand: jest.fn(),
    GetObjectCommand: jest.fn(),
    DeleteObjectCommand: jest.fn()
  };
});

// Mock S3 request presigner
jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn().mockResolvedValue('https://test-bucket.s3.amazonaws.com/test-file.zmt?presigned=true')
}));

// Mock compression scripts to avoid actual compression during tests
jest.mock('child_process', () => {
  const originalModule = jest.requireActual('child_process');
  
  return {
    ...originalModule,
    spawn: jest.fn((command, args, options) => {
      // Mock compression script execution
      if (command.includes('zmt') || command.includes('compress')) {
        const mockProcess = {
          stdout: {
            on: jest.fn((event, callback) => {
              if (event === 'data') {
                // Simulate compression output
                callback(Buffer.from('Compression progress: 50%\n'));
                callback(Buffer.from('Compression completed\n'));
              }
            })
          },
          stderr: {
            on: jest.fn()
          },
          on: jest.fn((event, callback) => {
            if (event === 'close') {
              // Simulate successful compression
              setTimeout(() => callback(0), 100);
            }
          })
        };
        return mockProcess;
      }
      
      // For other commands, use original spawn
      return originalModule.spawn(command, args, options);
    })
  };
});

// Increase timeout for integration tests
jest.setTimeout(60000);

// Global test utilities
global.testUtils = {
  createTestFile: (filename, content) => {
    const fs = require('fs');
    const path = require('path');
    const testDir = path.join(__dirname, 'test-files');
    
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const filePath = path.join(testDir, filename);
    fs.writeFileSync(filePath, content);
    return filePath;
  },
  
  cleanupTestFiles: () => {
    const fs = require('fs');
    const path = require('path');
    const testDir = path.join(__dirname, 'test-files');
    
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
  }
};

// Cleanup after all tests
afterAll(() => {
  if (global.testUtils) {
    global.testUtils.cleanupTestFiles();
  }
});
